const User = require("../models/userModel");
const XPTransaction = require("../models/xpTransactionModel");
const LevelDefinition = require("../models/levelDefinitionModel");
const Report = require("../models/reportModel");

class XPRankingService {
  constructor() {
    // Enhanced ranking weights based on real performance metrics
    this.weights = {
      // Core Performance Metrics
      totalXP: 1.0,                    // Base XP weight (from actual quiz performance)
      currentLevel: 30,                // Level bonus (reduced from artificial inflation)
      averageScore: 1.5,               // Average score weight

      // Engagement & Consistency Metrics
      quizCompletionRate: 2.0,         // Completion rate bonus
      loginConsistency: 1.2,           // Login streak bonus
      studyConsistency: 1.0,           // Study time consistency

      // Achievement & Mastery Metrics
      achievementBonus: 15,            // Achievement bonus (reduced)
      subjectMastery: 2.5,             // Subject expertise bonus
      perfectScoreBonus: 3.0,          // Perfect score frequency bonus

      // Activity & Participation
      recentActivity: 1.0,             // Recent activity bonus
      totalParticipation: 0.5,         // Total quiz participation

      // Premium Status (minimal impact)
      premiumBonus: 25,                // Premium user bonus (reduced significantly)
    };

    // Season configuration
    this.seasonConfig = {
      currentSeason: "2024-S1",
      seasonWeight: 0.8,               // Higher weight for current season
      lifetimeWeight: 0.2,             // Lower weight for lifetime (focus on current performance)
    };

    // Performance tiers for additional bonuses
    this.performanceTiers = {
      bronze: { minXP: 0, multiplier: 1.0 },
      silver: { minXP: 1000, multiplier: 1.05 },
      gold: { minXP: 2500, multiplier: 1.1 },
      platinum: { minXP: 5000, multiplier: 1.15 },
      diamond: { minXP: 10000, multiplier: 1.2 },
    };
  }

  /**
   * Calculate enhanced ranking score based on real performance metrics
   */
  async calculateRankingScore(user) {
    try {
      // Core XP Score (weighted by season vs lifetime)
      const seasonalScore = (user.seasonXP || 0) * this.seasonConfig.seasonWeight;
      const lifetimeScore = (user.lifetimeXP || 0) * this.seasonConfig.lifetimeWeight;
      const baseXPScore = (seasonalScore + lifetimeScore) * this.weights.totalXP;

      // Level bonus (reduced impact)
      const levelBonus = (user.currentLevel || 1) * this.weights.currentLevel;

      // Average score bonus
      const averageScoreBonus = (user.averageScore || 0) * this.weights.averageScore;

      // Enhanced engagement metrics
      const engagementScore = await this.calculateEngagementScore(user);

      // Subject mastery bonus
      const masteryScore = this.calculateSubjectMasteryScore(user);

      // Achievement bonus (reduced impact)
      const achievementCount = user.achievements ? user.achievements.length : 0;
      const achievementBonus = achievementCount * this.weights.achievementBonus;

      // Perfect score frequency bonus
      const perfectScoreBonus = this.calculatePerfectScoreBonus(user);

      // Recent activity bonus
      const activityBonus = await this.calculateActivityBonus(user._id);

      // Premium user bonus (minimal impact)
      const premiumBonus = this.isPremiumUser(user) ? this.weights.premiumBonus : 0;

      // Performance tier multiplier
      const tierMultiplier = this.getPerformanceTierMultiplier(user.totalXP || 0);

      // Calculate base ranking score
      const baseScore = Math.round(
        baseXPScore +
        levelBonus +
        averageScoreBonus +
        engagementScore +
        masteryScore +
        achievementBonus +
        perfectScoreBonus +
        activityBonus +
        premiumBonus
      );

      // Apply tier multiplier
      const finalScore = Math.round(baseScore * tierMultiplier);

      return {
        success: true,
        rankingScore: finalScore,
        breakdown: {
          baseXPScore: Math.round(baseXPScore),
          levelBonus: Math.round(levelBonus),
          averageScoreBonus: Math.round(averageScoreBonus),
          engagementScore: Math.round(engagementScore),
          masteryScore: Math.round(masteryScore),
          achievementBonus: Math.round(achievementBonus),
          perfectScoreBonus: Math.round(perfectScoreBonus),
          activityBonus: Math.round(activityBonus),
          premiumBonus: Math.round(premiumBonus),
          tierMultiplier: tierMultiplier,
          baseScore: baseScore,
          finalScore: finalScore
        }
      };

    } catch (error) {
      console.error('Error calculating ranking score:', error);
      return { rankingScore: 0, breakdown: {} };
    }
  }

  /**
   * Calculate recent activity bonus
   */
  async calculateActivityBonus(userId) {
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const recentTransactions = await XPTransaction.find({
        user: userId,
        createdAt: { $gte: sevenDaysAgo }
      });

      const recentXP = recentTransactions.reduce((sum, transaction) => 
        sum + transaction.xpAmount, 0
      );

      return recentXP * this.weights.activityBonus;

    } catch (error) {
      console.error('Error calculating activity bonus:', error);
      return 0;
    }
  }

  /**
   * Check if user is premium
   */
  isPremiumUser(user) {
    const premiumStatuses = ['premium', 'active'];
    const hasValidSubscription = premiumStatuses.includes(user.subscriptionStatus);

    if (!hasValidSubscription) return false;

    // Check subscription end date if it exists
    if (user.subscriptionEndDate) {
      return new Date(user.subscriptionEndDate) > new Date();
    }

    return true;
  }

  /**
   * Calculate engagement score based on activity tracking
   */
  async calculateEngagementScore(user) {
    if (!user.activityTracking) return 0;

    const activity = user.activityTracking;
    let engagementScore = 0;

    // Login consistency bonus
    const loginStreak = activity.dailyLoginStreak || 0;
    const loginConsistencyBonus = Math.min(loginStreak * this.weights.loginConsistency, 100);
    engagementScore += loginConsistencyBonus;

    // Quiz completion rate bonus
    const totalQuizzes = user.totalQuizzesTaken || 0;
    const passedQuizzes = activity.quizzesPassed || 0;
    const completionRate = totalQuizzes > 0 ? (passedQuizzes / totalQuizzes) * 100 : 0;
    const completionBonus = completionRate * this.weights.quizCompletionRate;
    engagementScore += completionBonus;

    // Study consistency bonus
    const weeklyQuizzes = activity.weeklyStats?.quizzesThisWeek || 0;
    const monthlyQuizzes = activity.monthlyStats?.quizzesThisMonth || 0;
    const consistencyBonus = (weeklyQuizzes * 5) + (monthlyQuizzes * 2);
    engagementScore += consistencyBonus * this.weights.studyConsistency;

    // Total participation bonus
    const participationBonus = totalQuizzes * this.weights.totalParticipation;
    engagementScore += participationBonus;

    return Math.round(engagementScore);
  }

  /**
   * Calculate subject mastery score
   */
  calculateSubjectMasteryScore(user) {
    if (!user.activityTracking?.subjectPerformance) return 0;

    const subjects = user.activityTracking.subjectPerformance;
    let masteryScore = 0;

    subjects.forEach(subject => {
      if (subject.quizzesTaken >= 3) { // Minimum quizzes for mastery consideration
        const avgScore = subject.averageScore || 0;
        const quizCount = subject.quizzesTaken || 0;

        // Mastery bonus based on average score and quiz count
        let subjectBonus = 0;
        if (avgScore >= 95) subjectBonus = quizCount * 5; // Master level
        else if (avgScore >= 85) subjectBonus = quizCount * 4; // Expert level
        else if (avgScore >= 75) subjectBonus = quizCount * 3; // Advanced level
        else if (avgScore >= 65) subjectBonus = quizCount * 2; // Intermediate level
        else subjectBonus = quizCount * 1; // Beginner level

        masteryScore += subjectBonus;
      }
    });

    return Math.round(masteryScore * this.weights.subjectMastery);
  }

  /**
   * Calculate perfect score bonus
   */
  calculatePerfectScoreBonus(user) {
    if (!user.activityTracking) return 0;

    const perfectStreak = user.activityTracking.perfectScoreStreak || 0;
    const bestPerfectStreak = user.activityTracking.bestPerfectStreak || 0;

    // Bonus for current perfect streak and historical best
    const currentBonus = perfectStreak * 10;
    const historicalBonus = bestPerfectStreak * 5;

    return Math.round((currentBonus + historicalBonus) * this.weights.perfectScoreBonus);
  }

  /**
   * Get performance tier multiplier
   */
  getPerformanceTierMultiplier(totalXP) {
    for (const [tier, config] of Object.entries(this.performanceTiers).reverse()) {
      if (totalXP >= config.minXP) {
        return config.multiplier;
      }
    }
    return this.performanceTiers.bronze.multiplier;
  }

  /**
   * Get comprehensive leaderboard with XP-based ranking
   */
  async getXPLeaderboard(options = {}) {
    try {
      const {
        limit = 100,
        classFilter = null,
        levelFilter = null,
        seasonFilter = null,
        includeInactive = false
      } = options;

      // Build match criteria - exclude admins and blocked users
      const matchCriteria = {
        isAdmin: { $ne: true },
        isBlocked: { $ne: true }
      };

      if (classFilter) {
        matchCriteria.class = classFilter;
      }

      if (levelFilter) {
        // Handle both capitalized and lowercase level values
        const normalizedLevel = levelFilter.toLowerCase();
        matchCriteria.$or = [
          { level: levelFilter },
          { level: normalizedLevel },
          { level: levelFilter.charAt(0).toUpperCase() + levelFilter.slice(1).toLowerCase() }
        ];
      }

      if (seasonFilter) {
        matchCriteria.currentSeason = seasonFilter;
      }

      if (!includeInactive) {
        // Include users with either quiz activity or XP
        matchCriteria.$or = [
          { totalQuizzesTaken: { $gt: 0 } },
          { totalXP: { $gt: 0 } }
        ];
      }

      // Aggregation pipeline for enhanced ranking
      const pipeline = [
        { $match: matchCriteria },

        // Add computed fields for better ranking calculation
        {
          $addFields: {
            // Ensure all numeric fields have default values
            totalXP: { $ifNull: ["$totalXP", 0] },
            currentLevel: { $ifNull: ["$currentLevel", 1] },
            averageScore: { $ifNull: ["$averageScore", 0] },
            bestStreak: { $ifNull: ["$bestStreak", 0] },
            currentStreak: { $ifNull: ["$currentStreak", 0] },
            totalQuizzesTaken: { $ifNull: ["$totalQuizzesTaken", 0] },
            totalPointsEarned: { $ifNull: ["$totalPointsEarned", 0] },
            lifetimeXP: { $ifNull: ["$lifetimeXP", "$totalXP"] },
            seasonXP: { $ifNull: ["$seasonXP", { $multiply: ["$totalXP", 0.3] }] },

            // Normalize subscription status with proper logic
            normalizedSubscriptionStatus: {
              $switch: {
                branches: [
                  // Active/Premium users
                  {
                    case: {
                      $and: [
                        { $in: ["$subscriptionStatus", ["active", "premium"]] },
                        {
                          $or: [
                            { $eq: ["$subscriptionEndDate", null] }, // No end date
                            { $gte: ["$subscriptionEndDate", new Date()] } // End date in future
                          ]
                        }
                      ]
                    },
                    then: "premium"
                  },
                  // Expired users (had subscription but now expired)
                  {
                    case: {
                      $or: [
                        { $eq: ["$subscriptionStatus", "expired"] },
                        {
                          $and: [
                            { $in: ["$subscriptionStatus", ["active", "premium"]] },
                            { $ne: ["$subscriptionEndDate", null] },
                            { $lt: ["$subscriptionEndDate", new Date()] }
                          ]
                        },
                        // Users who have subscriptionEndDate but status is not active/premium
                        {
                          $and: [
                            { $ne: ["$subscriptionEndDate", null] },
                            { $not: { $in: ["$subscriptionStatus", ["active", "premium"]] } }
                          ]
                        }
                      ]
                    },
                    then: "expired"
                  }
                ],
                // Default to free (users who never had a subscription)
                default: "free"
              }
            },

            // Calculate achievement count
            achievementCount: {
              $cond: {
                if: { $isArray: "$achievements" },
                then: { $size: "$achievements" },
                else: 0
              }
            }
          }
        },
        
        // Calculate ranking score components
        {
          $addFields: {
            // Base XP calculation
            baseXPScore: { $multiply: ["$totalXP", this.weights.totalXP] },

            // Level bonus
            levelBonus: { $multiply: ["$currentLevel", this.weights.currentLevel] },

            // Average score bonus
            averageScoreBonus: { $multiply: ["$averageScore", this.weights.averageScore] },

            // Streak bonus
            streakBonus: { $multiply: ["$bestStreak", this.weights.streakBonus] },

            // Achievement bonus
            achievementBonus: { $multiply: ["$achievementCount", this.weights.achievementBonus] },

            // Premium bonus
            premiumBonus: {
              $cond: [
                { $eq: ["$normalizedSubscriptionStatus", "premium"] },
                this.weights.premiumBonus,
                0
              ]
            },

            // Activity bonus (based on recent quiz activity)
            activityBonus: {
              $cond: [
                { $gte: ["$totalQuizzesTaken", 5] },
                { $multiply: ["$totalQuizzesTaken", this.weights.activityBonus] },
                0
              ]
            },

            // Weighted XP (seasonal + lifetime)
            weightedXP: {
              $add: [
                { $multiply: ["$seasonXP", this.seasonConfig.seasonWeight] },
                { $multiply: ["$lifetimeXP", this.seasonConfig.lifetimeWeight] }
              ]
            }
          }
        },
        
        // Calculate final ranking score
        {
          $addFields: {
            rankingScore: {
              $round: {
                $add: [
                  "$weightedXP",
                  "$levelBonus",
                  "$averageScoreBonus",
                  "$streakBonus",
                  "$achievementBonus",
                  "$activityBonus",
                  "$premiumBonus"
                ]
              }
            }
          }
        },
        
        // Sort by ranking score
        { $sort: { rankingScore: -1, totalXP: -1, name: 1 } },
        
        // Limit results
        { $limit: limit },
        
        // Add rank field
        {
          $group: {
            _id: null,
            users: { $push: "$$ROOT" }
          }
        },
        {
          $unwind: {
            path: "$users",
            includeArrayIndex: "rank"
          }
        },
        {
          $addFields: {
            "users.rank": { $add: ["$rank", 1] }
          }
        },
        {
          $replaceRoot: { newRoot: "$users" }
        },
        
        // Lookup active subscription with plan details
        {
          $lookup: {
            from: "subscriptions",
            let: { userId: "$_id" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$user", "$$userId"] },
                      { $eq: ["$status", "active"] },
                      { $eq: ["$paymentStatus", "paid"] }
                    ]
                  }
                }
              },
              {
                $lookup: {
                  from: "plans",
                  localField: "activePlan",
                  foreignField: "_id",
                  as: "planDetails"
                }
              },
              {
                $unwind: {
                  path: "$planDetails",
                  preserveNullAndEmptyArrays: true
                }
              },
              {
                $project: {
                  planTitle: "$planDetails.title",
                  planDuration: "$planDetails.duration",
                  endDate: 1,
                  status: 1
                }
              }
            ],
            as: "activeSubscription"
          }
        },

        // Project final fields
        {
          $project: {
            _id: 1,
            userId: "$_id", // Add userId field for compatibility
            name: 1,
            class: 1,
            level: 1,
            school: 1,
            profileImage: 1,
            profilePicture: "$profileImage", // Add alias for compatibility
            email: 1,

            // Subscription Info
            subscriptionStatus: "$normalizedSubscriptionStatus",
            subscriptionEndDate: 1,
            subscriptionPlan: 1,
            // Add active subscription plan details
            activePlanTitle: {
              $arrayElemAt: ["$activeSubscription.planTitle", 0]
            },
            activePlanDuration: {
              $arrayElemAt: ["$activeSubscription.planDuration", 0]
            },

            // XP and Level Info
            totalXP: 1,
            currentLevel: 1,
            xpToNextLevel: 1,
            seasonXP: 1,
            lifetimeXP: 1,

            // Legacy Points (for backward compatibility)
            totalPoints: "$totalPointsEarned",
            totalPointsEarned: 1,

            // Statistics
            totalQuizzesTaken: 1,
            quizzesTaken: "$totalQuizzesTaken", // Add alias
            averageScore: 1,
            bestStreak: 1,
            currentStreak: 1,
            achievements: 1,
            achievementCount: 1,

            // Additional stats for UI
            passedExamsCount: { $ifNull: ["$passedExamsCount", 0] },
            retryCount: { $ifNull: ["$retryCount", 0] },

            // Ranking Info
            rank: 1,
            rankingScore: 1,
            score: "$rankingScore", // Add alias for compatibility

            // Score Breakdown
            breakdown: {
              baseXP: "$baseXPScore",
              weightedXP: "$weightedXP",
              levelBonus: "$levelBonus",
              averageScoreBonus: "$averageScoreBonus",
              streakBonus: "$streakBonus",
              achievementBonus: "$achievementBonus",
              activityBonus: "$activityBonus",
              premiumBonus: "$premiumBonus"
            },

            // Timestamps
            createdAt: 1,
            updatedAt: 1
          }
        }
      ];

      const leaderboard = await User.aggregate(pipeline);
      
      return {
        success: true,
        data: leaderboard,
        metadata: {
          totalUsers: leaderboard.length,
          season: this.seasonConfig.currentSeason,
          weights: this.weights,
          generatedAt: new Date()
        }
      };

    } catch (error) {
      console.error('Error generating XP leaderboard:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Get user's ranking position and nearby users
   */
  async getUserRankingPosition(userId, context = 5) {
    try {
      const leaderboard = await this.getXPLeaderboard({ limit: 10000 });
      
      if (!leaderboard.success) {
        throw new Error('Failed to generate leaderboard');
      }

      const userIndex = leaderboard.data.findIndex(user => 
        user._id.toString() === userId.toString()
      );

      if (userIndex === -1) {
        return {
          success: false,
          message: 'User not found in rankings'
        };
      }

      const userRank = userIndex + 1;
      const startIndex = Math.max(0, userIndex - context);
      const endIndex = Math.min(leaderboard.data.length, userIndex + context + 1);
      
      const nearbyUsers = leaderboard.data.slice(startIndex, endIndex);

      return {
        success: true,
        userRank: userRank,
        totalUsers: leaderboard.data.length,
        user: leaderboard.data[userIndex],
        nearbyUsers: nearbyUsers,
        context: {
          showingFrom: startIndex + 1,
          showingTo: endIndex,
          contextSize: context
        }
      };

    } catch (error) {
      console.error('Error getting user ranking position:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get class-specific rankings
   */
  async getClassRankings(className, limit = 50) {
    return this.getXPLeaderboard({
      limit: limit,
      classFilter: className
    });
  }

  /**
   * Get seasonal rankings
   */
  async getSeasonalRankings(season = null, limit = 100) {
    const targetSeason = season || this.seasonConfig.currentSeason;
    return this.getXPLeaderboard({
      limit: limit,
      seasonFilter: targetSeason
    });
  }
}

module.exports = new XPRankingService();
