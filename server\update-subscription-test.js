const mongoose = require('mongoose');
const User = require('./models/userModel');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const updateSubscriptionStatuses = async () => {
  try {
    await connectDB();
    
    console.log('🔄 Updating subscription statuses for testing...\n');
    
    // Get all non-admin users
    const users = await User.find({ isAdmin: { $ne: true } }).limit(10);
    
    if (users.length === 0) {
      console.log('❌ No users found');
      return;
    }
    
    console.log(`👥 Found ${users.length} users to update`);
    
    // Update users with different subscription statuses
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      let subscriptionStatus, subscriptionEndDate;
      
      if (i % 3 === 0) {
        // Premium users (active subscription)
        subscriptionStatus = 'premium';
        subscriptionEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
      } else if (i % 3 === 1) {
        // Expired users
        subscriptionStatus = 'expired';
        subscriptionEndDate = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000); // 10 days ago
      } else {
        // Free users
        subscriptionStatus = 'free';
        subscriptionEndDate = null;
      }
      
      await User.findByIdAndUpdate(user._id, {
        subscriptionStatus: subscriptionStatus,
        subscriptionEndDate: subscriptionEndDate
      });
      
      console.log(`✅ Updated ${user.name}: ${subscriptionStatus} ${subscriptionEndDate ? `(ends: ${subscriptionEndDate.toDateString()})` : ''}`);
    }
    
    console.log('\n🎉 Subscription statuses updated successfully!');
    
    // Show summary
    const premiumCount = await User.countDocuments({ 
      subscriptionStatus: 'premium',
      isAdmin: { $ne: true }
    });
    
    const expiredCount = await User.countDocuments({ 
      subscriptionStatus: 'expired',
      isAdmin: { $ne: true }
    });
    
    const freeCount = await User.countDocuments({ 
      subscriptionStatus: 'free',
      isAdmin: { $ne: true }
    });
    
    console.log('\n📊 Summary:');
    console.log(`🔵 Premium users: ${premiumCount}`);
    console.log(`🔴 Expired users: ${expiredCount}`);
    console.log(`🟢 Free users: ${freeCount}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Update failed:', error);
    process.exit(1);
  }
};

updateSubscriptionStatuses();
